<div class="home-page">

  <!-- Header Section -->
  <header class="home-header">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
      <div class="container-fluid px-4">
        <!-- Logo -->
        <div class="navbar-brand">
          <img alt="Logo" src="./assets/media/easydeallogos/loading-logo.png" class="h-40px app-sidebar-logo-default" />
        </div>

        <!-- Mobile Menu Toggle Button -->
        <button class="navbar-toggler d-lg-none" type="button" (click)="toggleMobileMenu()"
          [attr.aria-expanded]="showMobileMenu" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon">
          </span>
        </button>

        <!-- Navigation Menu -->
        <div class="navbar-nav mx-auto d-none d-lg-flex">
          <ul class="nav-list d-flex align-items-center mb-0">
            <li class="nav-item">
              <a href="/home" class="nav-link"> Home </a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link"> About EasyDeal </a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link"> New Projects </a>
            </li>
            <li class="nav-item">
              <a href="javascript:void(0)" (click)="scrollToSection()" class="nav-link">
                Advertisements </a>
            </li>
            <li class="nav-item">
              <a href="javascript:void(0)" (click)="scrollToFooter()" class="nav-link"> Contact Us </a>
            </li>
          </ul>
        </div>

        <!-- Mobile Navigation Dropdown -->
        <div *ngIf="showMobileMenu" class="mobile-nav-dropdown d-lg-none">
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-home fs-6 text-primary"></i>
            </span>
            <span>Home</span>
          </div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-info-circle fs-6 text-info"></i>
            </span>
            <span>About EasyDeal</span>
          </div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-building fs-6 text-success"></i>
            </span>
            <span>New Projects</span>
          </div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-bullhorn fs-6 text-warning"></i>
            </span>
            <span>Advertisements</span>
          </div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-phone fs-6 text-gray-600"></i>
            </span>
            <span>Contact Us</span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-globe fs-6 text-primary"></i>
            </span>
            <span>العربية / English</span>
          </div>
        </div>

        <!-- Language Toggle & User Section -->
        <div class="navbar-nav position-relative d-flex align-items-center">
          <!-- Language Toggle Button -->
          <div class="language-toggle me-3">
            <button class="btn btn-outline-light language-btn" type="button">
              <i class="fas fa-globe me-2"></i>
              <span class="language-text">English</span>
              <i class="fas fa-chevron-down ms-2"></i>
            </button>
            <!-- Language Dropdown -->
            <div class="language-dropdown">
              <div class="dropdown-item language-option active">
                <i class="fas fa-check me-2 text-success"></i>
                <span>English</span>
              </div>
              <div class="dropdown-item language-option">
                <i class="fas fa-language me-2 text-primary"></i>
                <span>العربية</span>
              </div>
            </div>
          </div>
          <!-- If user is logged in, show user profile -->
          <div *ngIf="isLoggedIn" class="nav-link user-profile" (click)="toggleUserDropdown()">
            <img [src]="getUserProfileImage()" [alt]="getUserDisplayName()" class="user-avatar me-2">
            <span class="user-name">{{ getUserDisplayName() }}</span>
            <i class="fas fa-chevron-down ms-2"></i>
          </div>

          <!-- User Dropdown Menu -->
          <div *ngIf="isLoggedIn && showUserDropdown" class="user-dropdown">
            <div class="dropdown-item" (click)="closeUserDropdown()">
              <span class="menu-icon me-2">
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_24_2533)">
                    <path stroke="#e74c3c" stroke-width="1"
                      d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
                    <path stroke="#e74c3c" stroke-width="1"
                      d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
                    <path stroke="#e74c3c" stroke-width="1"
                      d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_24_2533">
                      <rect width="19" height="19" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <span>Requests</span>
            </div>
            <div class="dropdown-item" (click)="closeUserDropdown()">
              <span class="menu-icon me-2">
                <app-keenicon name="user" class="fs-5 text-primary" type="outline"></app-keenicon>
              </span>
              <span> My Profile </span>
            </div>
            <div class="dropdown-item" (click)="closeUserDropdown()">
              <span class="menu-icon me-2">
                <app-keenicon name="messages" class="fs-5 text-info" type="outline"></app-keenicon>
              </span>
              <span> Messages </span>
            </div>
            <div class="dropdown-item" (click)="closeUserDropdown()">
              <span class="menu-icon me-2">
                <i class="fa-regular fa-circle-question fs-6 text-warning"></i>
              </span>
              <span> Help </span>
            </div>
            <div class="dropdown-item" (click)="closeUserDropdown()">
              <span class="menu-icon me-2">
                <app-keenicon name="notification-on" class="fs-5 text-gray-600" type="outline"></app-keenicon>
              </span>
              <span> Notifications </span>
            </div>
            <div class="dropdown-divider"></div>
            <div class="dropdown-item logout-item" (click)="logout()">
              <span class="menu-icon me-2">
                <i class="fas fa-sign-out-alt fs-6 text-danger"></i>
              </span>
              <span> Logout </span>
            </div>
            <div class="dropdown-divider"></div>
            <div class="dropdown-item new-request-item" (click)="closeUserDropdown()">
              <span class="text-success"> New Request </span>
            </div>
          </div>

          <!-- If user is not logged in, show register button -->
          <a *ngIf="!isLoggedIn" href="#" class="nav-link user-link">
            <i class="fas fa-user me-2"></i>
            Register Guest
          </a>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section">
      <div class="hero-background">
        <img
          src="./assets/media/home/<USER>"
          alt="Hero Background" class="hero-bg-image">
        <div class="hero-overlay"></div>
      </div>

      <div class="hero-content">
        <div class="container">
          <div class="row justify-content-center">
            <div class="col-12">
              <div class="hero-text-container">
                <div class="hero-text-item">
                  <h2 class="hero-text"> Easy</h2>
                </div>
                <div class="hero-text-item">
                  <h2 class="hero-text"> Speed </h2>
                </div>
                <div class="hero-text-item">
                  <h2 class="hero-text"> Reliability </h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Properties Section -->
  <section id="properties-section" class="properties-section">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <h2 class="section-title text-center mb-5">Featured Properties</h2>
        </div>
      </div>

      <div class="row g-4">
        <!-- Dynamic Property Cards -->
        <div class="col-lg-3 col-md-6 col-sm-12" *ngFor="let property of properties; let i = index">
          <div class="property-card" (click)="onPropertyClick(property)" style="cursor: pointer;">
            <div class="property-image">
              <img src="{{ property.gallery?.[0]?.url || 'assets/media/auth/404-error.png' }}"
                [alt]="'Property ' + (i + 1)" class="img-fluid">
              <div class="property-badge">
                {{ property.unitOperation }}
              </div>
              <div class="property-location">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ property.area?.name_en || property.city?.name_en }}</span>
              </div>
            </div>
            <div class="property-content">
              <h4 class="property-title">{{ (property.type || '').slice(0, 20) | uppercase }}</h4>
              <p class="property-description">{{ property.numberOfRooms }} Rooms • {{ property.unitArea }} sqm</p>
              <div class="property-price">
                <span class="price">{{ (property.totalPriceInCash || property.monthlyRent || 0) | number:'1.0-0' }}
                  EGP</span>
              </div>
              <div class="property-rating-actions">
                <div class="property-rating">
                  <div class="stars">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                  </div>
                  <span class="rating-text">5.0</span>
                </div>
                <div class="property-actions">
                  <button class="btn btn-outline-secondary btn-sm">
                    <i class="far fa-heart"></i>
                  </button>
                  <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-share-alt"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div class="col-12 text-center" *ngIf="properties.length === 0">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-3">Loading properties...</p>
        </div>


        <!-- Load More Button -->
        <div class="col-12 text-center" *ngIf="properties.length > 0">
          <button class="btn btn-secondary btn-lg" (click)="loadMoreProperties()">
            Load More Properties
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Explore Locations Section -->
  <section class="horizontal-carousel-section py-5">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <h2 class="section-title text-center mb-5">Explore Locations</h2>
        </div>
      </div>

      <div class="carousel-container position-relative">
        <!-- Bootstrap Carousel -->
        <div id="horizontalCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
          <div class="carousel-inner">
            <!-- Dynamic Slides -->
            <div class="carousel-item " *ngFor="let slide of locationSlides; let i = index" [class.active]="i === 0">
              <div class="row justify-content-center g-2 g-md-3 mt-5">
                <div class="col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6" *ngFor="let location of slide">
                  <div class="location-card" (click)="onLocationClick(location)">
                    <img [src]="getRandomAreaImage()" [alt]="location.area.en || location.area.ar" class="img-fluid">
                    <div class="location-overlay">
                      <div class="location-info">
                        <h5>{{ (location.area.en || location.area.ar).slice(0, 19) }}</h5>
                        <p><i class="fas fa-map-marker-alt"></i> {{ location.count || 0 }}
                          Properties Available</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Carousel Controls -->
          <button class="carousel-control-prev" type="button" data-bs-target="#horizontalCarousel" data-bs-slide="prev"
            (click)="prevSlide()">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#horizontalCarousel" data-bs-slide="next"
            (click)="nextSlide()">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
      </div>


    </div>
  </section>

  <!-- Articles Section -->
  <section class="articles-section py-5">
    <div class="container">
      <!-- Section Header -->
      <div class="row mb-5">
        <div class="col-12">
          <div class="section-header d-flex justify-content-between align-items-center">
            <div class="left-controls d-flex align-items-center">
              <button class="carousel-control-btn prev-btn" type="button" data-bs-target="#articlesCarousel"
                data-bs-slide="prev" (click)="prevArticleSlide()">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button class="carousel-control-btn next-btn ms-2" type="button" data-bs-target="#articlesCarousel"
                data-bs-slide="next" (click)="nextArticleSlide()">
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>

            <h1 class="articles-title text-center flex-grow-1  ">Articles That Interest You</h1>
          </div>
        </div>
      </div>

      <!-- Bootstrap Carousel for Articles with All Articles Link -->
      <div class="row">
        <!-- All Articles Link on the Left -->
        <div class="col-lg-2 col-md-3 col-12 d-flex align-items-center justify-content-center">
          <div class="right-link">
            <!-- <a class="view-all-link">
              <i class="fas fa-arrow-left me-2 text-success fs-4"></i>
              <span class="text-success fs-2">All Articles</span>
              <div class="green-underline"></div>
            </a> -->
          </div>
        </div>

        <!-- Carousel on the Right -->
        <div class="col-lg-10 col-md-9 col-12">
          <div id="articlesCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="6000">
            <div class="carousel-inner">
              <!-- Slide 1 -->
              <div class="carousel-item active">
                <div class="row g-4 justify-content-center">
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/1.jpg" alt="Modern Villa" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Modern Finishing Materials - Shop with the Best</h4>
                            <p>A very quiet area away from the noise and hustle of the city, suitable for large and
                              small
                              families, spacious area with a private garden.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/2.jpg" alt="Hotel Property" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Invest Your Money with Hotel Property</h4>
                            <p>Excellent investment opportunity in the heart of the city, guaranteed returns and
                              integrated
                              management, strategic location near the airport and commercial centers.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/3.jpg" alt="Villa October" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Villa 10 October April 2019</h4>
                            <p>Latest international finishing materials, high quality and competitive prices,
                              specialized
                              team to implement finishing works to the highest standards.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Slide 2 -->
              <div class="carousel-item">
                <div class="row g-4 justify-content-center">
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/4.jpg" alt="New Cairo Apartment" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Apartment in New Cairo</h4>
                            <p>Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated
                              facilities, close to universities and international schools.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/5.jpg" alt="North Coast Villa" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>North Coast Properties</h4>
                            <p>Residential units directly on the sea, wonderful panoramic view, integrated recreational
                              facilities and suitable for summer investment.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/6.jpg" alt="Downtown Office" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Administrative Offices Downtown</h4>
                            <p>Modern office spaces in the heart of Cairo, suitable for companies and institutions,
                              parking
                              and integrated service facilities.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Slide 3 -->
              <div class="carousel-item">
                <div class="row g-4 justify-content-center">
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/7.jpg" alt="Sheikh Zayed Villa" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Luxury Villas in Sheikh Zayed</h4>
                            <p>Exclusive villas with modern design, private gardens, and premium finishes in the heart
                              of Sheikh Zayed City.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/1.jpg" alt="Maadi Commercial" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Commercial Spaces in Maadi</h4>
                            <p>Prime commercial locations in Maadi with high foot traffic, perfect for retail businesses
                              and restaurants.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-8">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/2.jpg" alt="Heliopolis Investment" class="img-fluid">
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>Investment Opportunities in Heliopolis</h4>
                            <p>High-yield investment properties in Heliopolis with guaranteed rental income and
                              strategic locations.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
              <button type="button" data-bs-target="#articlesCarousel" data-bs-slide-to="0" class="active"></button>
              <button type="button" data-bs-target="#articlesCarousel" data-bs-slide-to="1"></button>
              <button type="button" data-bs-target="#articlesCarousel" data-bs-slide-to="2"></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Download App Section -->
  <section class="download-app-section py-5">
    <div class="container">
      <div class="row align-items-center">
        <!-- Left Side - App Download Info -->
        <div class="col-lg-6 col-md-12 mb-4 mb-lg-0">
          <div class="download-content">
            <div class="download-header mb-4">
              <h2 class="download-title">Download the Electronic App</h2>
              <p class="download-subtitle">
                Download our app to access the latest real estate offers and properties
              </p>
            </div>

            <!-- App Store Buttons -->
            <div class="app-store-buttons d-flex flex-wrap gap-3">
              <a href="#" class="app-store-btn">
                <div class="store-button app-store">
                  <div class="store-icon">
                    <i class="fab fa-apple"></i>
                  </div>
                  <div class="store-text">
                    <span class="download-text">Download on the</span>
                    <span class="store-name">App Store</span>
                  </div>
                </div>
              </a>
              <a href="#" class="google-play-btn">
                <div class="store-button google-play">
                  <div class="store-icon">
                    <i class="fab fa-google-play"></i>
                  </div>
                  <div class="store-text">
                    <span class="download-text">GET IT ON</span>
                    <span class="store-name">Google Play</span>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>

        <!-- Right Side - App Preview/Logo -->
        <div class="col-lg-6 col-md-12 d-flex justify-content-end">
          <div class="app-preview text-end">
            <div class="app-logo-container">
              <img src="./assets/media/easydeallogos/loading-logo.png" alt="EasyDeal App" class="app-logo">
            </div>
            <div class="app-info mt-3">
              <h4 class="app-name">EASY DEAL</h4>
              <p class="app-description">Your trusted real estate partner</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Newsletter Subscription Section -->
      <div class="row justify-content-center mt-5">
        <div class="col-lg-8 col-md-10 col-12">
          <div class="newsletter-container">
            <div class="row align-items-center g-2">
              <!-- Left Side - Newsletter Content -->
              <div class="col-md-7 col-12 text-center text-md-start">
                <h5 class="newsletter-title mb-1">Join Our Mailing List</h5>
                <small class="newsletter-subtitle text-muted">Get latest offers</small>
              </div>

              <!-- Right Side - Subscribe Form -->
              <div class="col-md-5 col-12">
                <div class="newsletter-form">
                  <div class="input-group input-group-sm">
                    <input type="email" class="form-control" placeholder="Your email" />
                    <button type="button" class="btn btn-subscribe" (click)="onSubscribeClick()">
                      Subscribe
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="footer-section" id="footer-section">
    <div class="container">
      <div class="row">
        <!-- Company Info -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="footer-brand">
            <img src="./assets/media/easydeallogos/loading-logo.png" alt="EasyDeal Logo" class="footer-logo mb-3">
            <h5 class="company-name">EASY DEAL</h5>
            <p class="company-description">
              Your trusted real estate partner in Egypt. We provide the best properties
              and investment opportunities with professional service.
            </p>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="col-lg-2 col-md-6 mb-4">
          <div class="footer-links">
            <h6 class="footer-title">Quick Links</h6>
            <ul class="footer-menu">
              <li><a href="#">Home</a></li>
              <li><a href="#">About EasyDeal</a></li>
              <li><a href="#">New Projects</a></li>
              <li><a href="#">Advertisements</a></li>
              <li><a href="#">Contact Us</a></li>
            </ul>
          </div>
        </div>

        <!-- Services -->
        <div class="col-lg-2 col-md-6 mb-4">
          <div class="footer-links">
            <h6 class="footer-title">Services</h6>
            <ul class="footer-menu">
              <li><a href="#">Property Search</a></li>
              <li><a href="#">Investment Consulting</a></li>
              <li><a href="#">Property Management</a></li>
              <li><a href="#">Legal Support</a></li>
              <li><a href="#">Financing Options</a></li>
            </ul>
          </div>
        </div>

        <!-- Contact Info -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="footer-contact">
            <h6 class="footer-title">Contact Information</h6>
            <div class="contact-item">
              <i class="fas fa-phone text-primary me-2"></i>
              <span>19888 - info&#64;easydeal.com</span>
            </div>
            <div class="contact-item">
              <i class="fas fa-envelope text-primary me-2"></i>
              <span>info&#64;easydeal.com</span>
            </div>
            <div class="contact-item">
              <i class="fas fa-map-marker-alt text-primary me-2"></i>
              <span>Cairo, Egypt</span>
            </div>

            <!-- Social Media Links -->
            <div class="social-links mt-3">
              <a href="#" class="social-link">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-linkedin-in"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-pinterest"></i>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <div class="row align-items-center">
          <div class="col-md-6">
            <p class="copyright-text mb-0">
              © 2025 EasyDeal. All rights reserved.
            </p>
          </div>
          <div class="col-md-6 text-md-end">
            <div class="footer-bottom-links">
              <a href="#">Privacy Policy</a>
              <a href="#">Terms of Service</a>
              <a href="#">Cookie Policy</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>

</div>
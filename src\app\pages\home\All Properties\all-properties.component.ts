import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { HomeService } from '../services/home.service';

@Component({
  selector: 'app-all-properties',
  templateUrl: './all-properties.component.html',
  styleUrls: ['./all-properties.component.scss']
})
export class AllPropertiesComponent implements OnInit {
  properties: any[] = [];
  isLoading: boolean = false;
  currentPage: number = 0;
  limit: number = 6;
  hasMoreProperties: boolean = true;
  searchText: string = '';
  filteredProperties: any[] = [];

  constructor(
    private homeService: HomeService,
    private cd: ChangeDetectorRef,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadProperties();
    this.filteredProperties = [...this.properties];
  }

  loadProperties(loadMore: boolean = false): void {
    this.isLoading = true;

    const offset = loadMore ? this.currentPage * this.limit : 0;

    this.homeService.getFeaturedProperties(this.limit, offset).subscribe({
      next: (response: any) => {
        console.log('Properties response:', response);

        if (loadMore) {
           this.properties = [...this.properties, ...(response.data || [])];
        } else {
           this.properties = response.data || [];
        }


        if (loadMore) {
          this.currentPage++;
        }

        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading properties:', error);
        this.isLoading = false;
        this.cd.detectChanges();
      }
    });
  }

  loadMoreProperties(): void {
    if (!this.isLoading && this.hasMoreProperties) {
      this.loadProperties(true);
    }
  }

  onPropertyClick(property: any): void {
    console.log('Property clicked:', property);

    this.router.navigate(['developer/projects/models/units/details'], {
      queryParams: {
        unitId: property.id
      }
    });
  }

  formatPrice(property: any): string {
    if (property.unitOperation === 'Rent') {
      return property.monthlyRent ? `${property.monthlyRent.toLocaleString()} EGP/month` : 'Price on request';
    } else {
      const price = property.totalPriceInCash || property.totalPriceInInstallment;
      return price ? `${price.toLocaleString()} EGP` : 'Price on request';
    }
  }

  getPropertyType(property: any): string {
    return (property.type || '').slice(0, 12);
  }

  getPropertyLocation(property: any): string {
    return property.area?.name_en || property.city?.name_en || 'Location not specified';
  }

  getPropertyImage(property: any): string {
    return property.gallery?.[0]?.url || 'assets/media/auth/404-error.png';
  }

  goBack(): void {
    this.router.navigate(['/home']);
  }
}


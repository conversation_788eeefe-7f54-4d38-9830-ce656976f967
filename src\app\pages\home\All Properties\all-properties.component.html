<!-- All Properties Page -->
<div class="container-fluid py-5">
  <!-- Header Section -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="display-4 fw-bold text-primary mb-2">All Properties</h1>
          <p class="text-muted fs-5">Discover your perfect property from our extensive collection</p>
        </div>
        <button class="btn btn-outline-primary btn-lg" (click)="goBack()">
          <i class="fas fa-arrow-left me-2"></i>
          Back to Home
        </button>
      </div>
    </div>
  </div>

  <!-- Search Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-center">
        <div class="search-container" style="max-width: 500px; width: 100%;">
          <div class="input-group input-group-lg">
            <span class="input-group-text bg-white border-end-0">
              <i class="fas fa-search text-muted"></i>
            </span>
            <input type="text" class="form-control border-start-0 ps-0"
              placeholder="Search properties by type, location, or features..." [(ngModel)]="searchText"
              (input)="onSearchChange()" style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border-radius: 0 8px 8px 0;">
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Loading Spinner -->
  <div class="row" *ngIf="isLoading && properties.length === 0">
    <div class="col-12 text-center py-5">
      <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 text-muted">Loading properties...</p>
    </div>
  </div>

  <!-- Properties Grid -->
  <div class="row g-4" *ngIf="filteredProperties.length > 0">
    <div class="col-lg-3 col-md-6 col-sm-12" *ngFor="let property of filteredProperties; let i = index">
      <div class="property-card h-100" (click)="onPropertyClick(property)" style="cursor: pointer;">
        <div class="property-image position-relative">
          <img [src]="getPropertyImage(property)" [alt]="'Property ' + (i + 1)" class="img-fluid w-100"
            style="height: 250px; object-fit: cover; border-radius: 12px 12px 0 0;">

          <!-- Property Badge -->
          <div class="property-badge position-absolute top-0 start-0 m-3">
            <span class="badge bg-primary fs-6 px-3 py-2">
              {{ property.unitOperation }}
            </span>
          </div>

          <!-- Property Location -->
          <div class="property-location position-absolute bottom-0 start-0 m-3">
            <div class="bg-dark bg-opacity-75 text-white px-3 py-2 rounded">
              <i class="fas fa-map-marker-alt me-2"></i>
              <span>{{ getPropertyLocation(property) }}</span>
            </div>
          </div>
        </div>

        <div class="property-content p-4">
          <!-- Property Title -->
          <h4 class="property-title fw-bold mb-3 text-truncate">
            {{ getPropertyType(property) | uppercase }}
          </h4>

          <!-- Property Details -->
          <div class="property-details mb-3">
            <div class="row g-2">
              <div class="col-6" *ngIf="property.compoundType">
                <small class="text-muted d-block">Type</small>
                <span class="fw-semibold">{{ property.compoundType }}</span>
              </div>
              <div class="col-6" *ngIf="property.numberOfRooms">
                <small class="text-muted d-block">Rooms</small>
                <span class="fw-semibold">{{ property.numberOfRooms }}</span>
              </div>
              <div class="col-6" *ngIf="property.unitArea">
                <small class="text-muted d-block">Area</small>
                <span class="fw-semibold">{{ property.unitArea }} m²</span>
              </div>
              <div class="col-6" *ngIf="property.numberOfBathrooms">
                <small class="text-muted d-block">Bathrooms</small>
                <span class="fw-semibold">{{ property.numberOfBathrooms }}</span>
              </div>
            </div>
          </div>

          <!-- Property Price -->
          <div class="property-price">
            <h5 class="text-primary fw-bold mb-0">
              {{ formatPrice(property) }}
            </h5>
          </div>

          <!-- View Details Button -->
          <div class="mt-3">
            <button class="btn btn-outline-primary w-100">
              <i class="fas fa-eye me-2"></i>
              View Details
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Load More Button -->
  <div class="row mt-5" *ngIf="hasMoreProperties && !isLoading">
    <div class="col-12 text-center">
      <button class="btn btn-primary btn-lg px-5" (click)="loadMoreProperties()">
        <i class="fas fa-plus me-2"></i>
        Load More Properties
      </button>
    </div>
  </div>

  <!-- Loading More Spinner -->
  <div class="row mt-3" *ngIf="isLoading && properties.length > 0">
    <div class="col-12 text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading more...</span>
      </div>
    </div>
  </div>

  <!-- No More Properties Message -->
  <div class="row mt-5" *ngIf="!hasMoreProperties && properties.length > 0">
    <div class="col-12 text-center">
      <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        You've seen all available properties!
      </div>
    </div>
  </div>

  <!-- No Properties Found -->
  <div class="row" *ngIf="!isLoading && filteredProperties.length === 0 && properties.length > 0">
    <div class="col-12 text-center py-5">
      <div class="empty-state">

        <h3 class="text-muted">No Properties Found</h3>
        <p class="text-muted">No properties match your search criteria. Try adjusting your search terms.</p>

      </div>
    </div>
  </div>

  <!-- No Properties Loaded -->
  <div class="row" *ngIf="!isLoading && properties.length === 0">
    <div class="col-12 text-center py-5">
      <div class="empty-state">
        <i class="fas fa-home fa-5x text-muted mb-4"></i>
        <h3 class="text-muted">No Properties Available</h3>
        <p class="text-muted">Sorry, we couldn't find any properties at the moment.</p>
        <button class="btn btn-primary mt-3" (click)="goBack()">
          <i class="fas fa-arrow-left me-2"></i>
          Back to Home
        </button>
      </div>
    </div>
  </div>
</div>
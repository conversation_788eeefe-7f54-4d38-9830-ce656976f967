import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HomeComponent } from './home.component';
import { AllPropertiesComponent } from './All Properties/all-properties.component';
import { SharedModule } from '../../_metronic/shared/shared.module';

@NgModule({
  declarations: [
    HomeComponent,
    AllPropertiesComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([
      {
        path: '',
        component: HomeComponent
      },
      {
        path: 'properties',
        component: AllPropertiesComponent
      }
    ])
  ]
})
export class HomeModule { }
